<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GSAP Letter Animation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <link rel="stylesheet" href="style.css"/>
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 200vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
        }

        .scroll-section {
            height: 150vh;
            display: flex;
            align-items: flex-start;
            justify-content: center;
        }

        .container {
            position: relative;
            width: 800px;
            height: 600px;
            border-radius: 20px;
            overflow: visible;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .letter {
            position: absolute;
            font-size: 120px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            user-select: none;
            cursor: pointer;
        }

        .position-marker {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #ff6b6b;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            opacity: 0.7;
        }

        .start-marker {
            background: #4ecdc4;
        }

        .end-marker {
            background: #ff6b6b;
        }

        .info {
            position: fixed;
            top: 20px;
            left: 20px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            line-height: 1.5;
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
            z-index: 100;
        }

        .scroll-indicator {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            animation: bounce 2s infinite;
            z-index: 100;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
            40% { transform: translateX(-50%) translateY(-10px); }
            60% { transform: translateX(-50%) translateY(-5px); }
        }
    </style>
</head>
<body>
    <div class="info">
        <div>📜 <strong>Scroll to animate!</strong></div>
        <div>🎯 All letters animate simultaneously</div>
        <div>⭐ Letters converge to center to spell "WeDes"</div>
    </div>
    
    <div class="scroll-indicator">
        <div>↓</div>
        <div>Scroll Down</div>
    </div>

    <div class="scroll-section">
        <div class="container">            
            <!-- The animated letters -->
            <div class="letter" id="letter-w">W</div>
            <div class="letter" id="letter-e1">e</div>
            <div class="letter" id="letter-d">D</div>
            <div class="letter" id="letter-e2">e</div>
            <div class="letter" id="letter-s">s</div>
        </div>
    </div>

    <div class="scroll">
        <div style="text-align: center; color: white; font-size: 48px; font-weight: bold; margin-top: 50px;">
            Animation Complete! 🎉
        </div>
    </div>

    <div class="wrapper">
        <div class="content">
          <section class="section hero"></section>
        </div>
        <div class="image-container">
          <img src="https://assets-global.website-files.com/63ec206c5542613e2e5aa784/643312a6bc4ac122fc4e3afa_main%20home.webp" alt="image">
        </div>
    </div>

    <script>
        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);

        // Initial positions for each letter (scattered around)
        const initialPositions = [
            { x: -800, y: -400, rotation: -45 }, // W
            { x: 800, y: -300, rotation: 60 },   // e
            { x: -700, y: 200, rotation: 30 },   // D
            { x: 900, y: 300, rotation: -75 },   // e
            { x: 0, y: -600, rotation: 90 }      // s
        ];

        // Final positions (center with proper spacing to spell "WeDes")
        const finalPositions = [
            { x: -240, y: 0, rotation: 0 }, // W
            { x: -120, y: 0, rotation: 0 }, // e
            { x: 0, y: 0, rotation: 0 },    // D
            { x: 120, y: 0, rotation: 0 },  // e
            { x: 240, y: 0, rotation: 0 }   // s
        ];

        const letters = document.querySelectorAll('.letter');

        // Set initial positions and properties for all letters
        letters.forEach((letter, index) => {
            gsap.set(letter, {
                x: initialPositions[index].x,
                y: initialPositions[index].y,
                rotation: initialPositions[index].rotation,
                scale: 0.5,
                opacity: 0
            });
        });

        // Create a single timeline for all letters to ensure perfect synchronization
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: ".container",
                start: "top topp",
                end: "bottom top",
                scrub: 2, // Smooth scrubbing
                pin: true,
                markers: true, // Set to true if you want to see the trigger points
                onComplete: () => {
                    // Final bounce effect for all letters
                    gsap.to(letters, {
                        scale: 1.1,
                        duration: 0.5,
                        ease: "back.out(1.7)",
                        yoyo: true,
                        repeat: 1
                    });
                }
            }
        });

        // Add all letter animations to the same timeline
        letters.forEach((letter, index) => {
            tl.to(letter, {
                x: finalPositions[index].x,
                y: finalPositions[index].y,
                rotation: finalPositions[index].rotation,
                scale: 1,
                opacity: 1,
                duration: 3,
                ease: "power2.inOut"
            }, 0); // The "0" ensures all animations start at the same time
        });

        // Optional: Add hover effects for individual letters
        letters.forEach((letter, index) => {
            letter.addEventListener('mouseenter', () => {
                gsap.to(letter, {
                    scale: 1.3,
                    rotation: finalPositions[index].rotation + 10,
                    duration: 0.3,
                    ease: "back.out(1.7)"
                });
            });

            letter.addEventListener('mouseleave', () => {
                gsap.to(letter, {
                    scale: 1,
                    rotation: finalPositions[index].rotation,
                    duration: 0.3,
                    ease: "back.out(1.7)"
                });
            });
        });

        window.addEventListener("load", () => {
            gsap
              .timeline({
                scrollTrigger: {
                  trigger: ".wrapper",
                  start: "top top",
                  end: "+=150%",
                  pin: true,
                  scrub: true,
                  markers: true
                }
              })
              .to("img", {
                scale: 2,
                z: 350,
                transformOrigin: "center center",
                ease: "power1.inOut"
              })
              .to(
                ".section.hero",
                {
                  scale: 1.1,
                  transformOrigin: "center center",
                  ease: "power1.inOut"
                },
                "<"
              );
        });
    </script>

</body>
</html>