<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scroll Cards Animation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            overflow-x: hidden;
        }

        .container {
            height: 500vh; /* 5x viewport height for scrolling */
            position: relative;
        }

        .fixed-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: radial-gradient(circle at center, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: 1;
        }

        .scroll-content {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .cards-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card {
            position: absolute;
            width: 120px;
            height: 120px;
            background: linear-gradient(145deg, #667eea, #764ba2);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .letter {
            color: #ffffff;
            font-size: 72px;
            font-weight: 700;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            user-select: none;
        }

        /* Initial positions - cards start from edges */
        .card:nth-child(1) {
            transform: translate(-800px, -400px) rotate(-15deg) scale(0.8);
        }
        .card:nth-child(2) {
            transform: translate(800px, -300px) rotate(20deg) scale(0.8);
        }
        .card:nth-child(3) {
            transform: translate(-700px, 200px) rotate(10deg) scale(0.8);
        }
        .card:nth-child(4) {
            transform: translate(900px, 300px) rotate(-25deg) scale(0.8);
        }
        .card:nth-child(5) {
            transform: translate(0px, -600px) rotate(5deg) scale(0.8);
        }
        .card:nth-child(6) {
            transform: translate(-600px, 0px) rotate(-10deg) scale(0.8);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            z-index: 100;
            transition: width 0.1s ease;
        }

        .scroll-indicator {
            position: fixed;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255,255,255,0.6);
            font-size: 14px;
            z-index: 50;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        .title {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            color: #ffffff;
            font-size: 48px;
            font-weight: 700;
            text-align: center;
            z-index: 50;
            opacity: 0;
            transition: opacity 0.6s ease;
        }

        @media (max-width: 768px) {
            .card {
                width: 80px;
                height: 80px;
            }
            
            .letter {
                font-size: 48px;
            }
            
            .title {
                font-size: 32px;
                top: 30px;
            }
            
            // Adjust final positions for mobile
            const finalPositionsMobile = [
                { x: -160, y: 0, rotation: 0 }, // W
                { x: -80, y: 0, rotation: 0 },  // e
                { x: 0, y: 0, rotation: 0 },    // D
                { x: 80, y: 0, rotation: 0 },   // e
                { x: 160, y: 0, rotation: 0 }   // s
            ];
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    <div class="fixed-background"></div>
    
    <div class="container">
        <div class="scroll-content">
            <div class="title" id="title">Scroll to Explore</div>
            <div class="scroll-indicator" id="scrollIndicator">↓ Scroll Down ↓</div>
            
            <div class="cards-container">
                <div class="card">
                    <div class="letter">W</div>
                </div>
                <div class="card">
                    <div class="letter">e</div>
                </div>
                <div class="card">
                    <div class="letter">D</div>
                </div>
                <div class="card">
                    <div class="letter">e</div>
                </div>
                <div class="card">
                    <div class="letter">s</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let scrollProgress = 0;
        const cards = document.querySelectorAll('.card');
        const progressBar = document.getElementById('progressBar');
        const title = document.getElementById('title');
        const scrollIndicator = document.getElementById('scrollIndicator');

        // Initial positions for each letter
        const initialPositions = [
            { x: -800, y: -400, rotation: -15 }, // W
            { x: 800, y: -300, rotation: 20 },   // e
            { x: -700, y: 200, rotation: 10 },   // D
            { x: 900, y: 300, rotation: -25 },   // e
            { x: 0, y: -600, rotation: 5 }       // s
        ];

        // Final positions (center with proper spacing to spell "WeDes")
        const finalPositions = [
            { x: -240, y: 0, rotation: 0 }, // W
            { x: -120, y: 0, rotation: 0 }, // e
            { x: 0, y: 0, rotation: 0 },    // D
            { x: 120, y: 0, rotation: 0 },  // e
            { x: 240, y: 0, rotation: 0 }   // s
        ];

        function updateAnimation() {
            const scrollTop = window.pageYOffset;
            const maxScroll = document.body.scrollHeight - window.innerHeight;
            scrollProgress = Math.min(scrollTop / maxScroll, 1);

            // Update progress bar
            progressBar.style.width = `${scrollProgress * 100}%`;

            // Show/hide title and scroll indicator
            if (scrollProgress > 0.1) {
                title.style.opacity = '1';
                scrollIndicator.style.opacity = '0';
            } else {
                title.style.opacity = '0';
                scrollIndicator.style.opacity = '1';
            }

            // Update card positions
            cards.forEach((card, index) => {
                const initial = initialPositions[index];
                const final = finalPositions[index];
                
                // Use easing function for smooth animation
                const easeProgress = easeInOutCubic(scrollProgress);
                
                const currentX = initial.x + (final.x - initial.x) * easeProgress;
                const currentY = initial.y + (final.y - initial.y) * easeProgress;
                const currentRotation = initial.rotation + (final.rotation - initial.rotation) * easeProgress;
                const currentScale = 0.8 + (1 - 0.8) * easeProgress;
                
                card.style.transform = `translate(${currentX}px, ${currentY}px) rotate(${currentRotation}deg) scale(${currentScale})`;
                card.style.opacity = 0.3 + (0.7 * easeProgress);
            });
        }

        function easeInOutCubic(t) {
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        }

        // Throttled scroll event
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateAnimation();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', onScroll);
        
        // Initialize animation
        updateAnimation();

        // Smooth scrolling for better experience
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>
</body>
</html>